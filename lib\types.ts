// 配置类型定义
export type ConfigType = 's-ui' | 'x-ui' | '3x-ui';

export type ProtocolType = 'http' | 'https';

export interface BaseConfig {
  id: string;
  name: string;
  type: ConfigType;
  url: string;
  groupId: string;
  createdAt: string;
  updatedAt: string;
}

export interface SUIConfig extends BaseConfig {
  type: 's-ui';
  api: string;
  protocol: ProtocolType;
  cert?: string;
}

export interface XUIConfig extends BaseConfig {
  type: 'x-ui';
  username: string;
  password: string;
  protocol: ProtocolType;
  cert?: string;
}

export interface ThreeXUIConfig extends BaseConfig {
  type: '3x-ui';
  username: string;
  password: string;
  protocol: ProtocolType;
  cert?: string;
}

export type Config = SUIConfig | XUIConfig | ThreeXUIConfig;

// 分组类型定义
export interface Group {
  id: string;
  name: string;
  order: number;
  createdAt: string;
  updatedAt: string;
}

// 主题类型定义
export type ThemeMode = 'light' | 'dark' | 'system';

// 语言类型定义
export type Language = 'en' | 'zh-CN' | 'zh-TW' | 'fa';

// 应用设置类型定义
export interface AppSettings {
  theme: ThemeMode;
  language: Language;
  isPro: boolean;
}

// 应用状态类型定义
export interface AppState {
  configs: Config[];
  groups: Group[];
  settings: AppSettings;
  selectedGroupId: string | null;
  isLoading: boolean;
}

// 表单数据类型定义
export interface ConfigFormData {
  name: string;
  url: string;
  protocol: ProtocolType;
  groupId: string | string[]; // 支持单选或多选分组
  // S-UI 特有字段
  api?: string;
  // X-UI 和 3X-UI 特有字段
  username?: string;
  password?: string;
  cert?: string;
}
