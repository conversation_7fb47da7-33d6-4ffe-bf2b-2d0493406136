import * as Crypto from 'expo-crypto';
import * as SecureStore from 'expo-secure-store';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { AppSettings, AppState, Config, ConfigFormData, ConfigType, Group } from './types';

// 存储键
const STORAGE_KEYS = {
  CONFIGS: 'app_configs',
  GROUPS: 'app_groups',
  SETTINGS: 'app_settings',
  SELECTED_GROUP: 'selected_group_id',
};

// 默认设置
const DEFAULT_SETTINGS: AppSettings = {
  theme: 'system',
  language: 'en',
  isPro: false,
};

// 默认分组 - 名称将通过翻译系统动态显示
const DEFAULT_GROUPS: Group[] = [
  {
    id: 'all',
    name: 'groups.all', // 使用翻译键，实际显示时会被翻译
    order: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

interface AppStore extends AppState {
  // 配置相关操作
  addConfig: (formData: ConfigFormData, type: ConfigType) => Promise<void>;
  updateConfig: (id: string, updates: Partial<Config>) => Promise<void>;
  deleteConfig: (id: string) => Promise<void>;
  
  // 分组相关操作
  addGroup: (name: string) => Promise<void>;
  updateGroup: (id: string, updates: Partial<Group>) => Promise<void>;
  deleteGroup: (id: string) => Promise<void>;
  reorderGroups: (groups: Group[]) => Promise<void>;
  
  // 选择分组
  setSelectedGroup: (groupId: string | null) => Promise<void>;
  
  // 设置相关操作
  updateSettings: (updates: Partial<AppSettings>) => Promise<void>;
  
  // 数据加载
  loadData: () => Promise<void>;
  
  // 获取过滤后的配置
  getFilteredConfigs: () => Config[];
}

export const useAppStore = create<AppStore>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    configs: [],
    groups: DEFAULT_GROUPS,
    settings: DEFAULT_SETTINGS,
    selectedGroupId: 'all',
    isLoading: true,

    // 配置相关操作
    addConfig: async (formData: ConfigFormData, type: ConfigType) => {
      const newConfig: Config = {
        id: await Crypto.randomUUID(),
        name: formData.name,
        type,
        url: formData.url,
        groupId: formData.groupId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...(type === 's-ui' 
          ? { 
              api: formData.api!, 
              protocol: formData.protocol,
              cert: formData.cert 
            }
          : { 
              username: formData.username!, 
              password: formData.password!,
              protocol: formData.protocol,
              cert: formData.cert 
            }
        ),
      } as Config;

      set((state) => ({
        configs: [...state.configs, newConfig],
      }));
    },

    updateConfig: async (id: string, updates: Partial<Config>) => {
      set((state) => ({
        configs: state.configs.map((config) =>
          config.id === id
            ? { ...config, ...updates, updatedAt: new Date().toISOString() }
            : config
        ),
      }));
    },

    deleteConfig: async (id: string) => {
      set((state) => ({
        configs: state.configs.filter((config) => config.id !== id),
      }));
    },

    // 分组相关操作
    addGroup: async (name: string) => {
      const { groups } = get();
      const maxOrder = Math.max(...groups.map(g => g.order));
      
      const newGroup: Group = {
        id: await Crypto.randomUUID(),
        name,
        order: maxOrder + 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      set((state) => ({
        groups: [...state.groups, newGroup],
      }));
    },

    updateGroup: async (id: string, updates: Partial<Group>) => {
      set((state) => ({
        groups: state.groups.map((group) =>
          group.id === id
            ? { ...group, ...updates, updatedAt: new Date().toISOString() }
            : group
        ),
      }));
    },

    deleteGroup: async (id: string) => {
      const { configs } = get();
      
      // 将该分组下的配置移动到全部分组
      const updatedConfigs = configs.map((config) =>
        config.groupId === id
          ? { ...config, groupId: 'all', updatedAt: new Date().toISOString() }
          : config
      );

      set((state) => ({
        groups: state.groups.filter((group) => group.id !== id),
        configs: updatedConfigs,
        selectedGroupId: state.selectedGroupId === id ? 'all' : state.selectedGroupId,
      }));
    },

    reorderGroups: async (groups: Group[]) => {
      set({ groups });
    },

    // 选择分组
    setSelectedGroup: async (groupId: string | null) => {
      set({ selectedGroupId: groupId });
    },

    // 设置相关操作
    updateSettings: async (updates: Partial<AppSettings>) => {
      set((state) => ({
        settings: { ...state.settings, ...updates },
      }));
    },

    // 数据加载
    loadData: async () => {
      try {
        set({ isLoading: true });

        // 并行加载所有数据
        const [configsData, groupsData, settingsData, selectedGroupData] = await Promise.all([
          SecureStore.getItemAsync(STORAGE_KEYS.CONFIGS),
          SecureStore.getItemAsync(STORAGE_KEYS.GROUPS),
          SecureStore.getItemAsync(STORAGE_KEYS.SETTINGS),
          SecureStore.getItemAsync(STORAGE_KEYS.SELECTED_GROUP),
        ]);

        const configs = configsData ? JSON.parse(configsData) : [];
        const groups = groupsData ? JSON.parse(groupsData) : DEFAULT_GROUPS;
        const settings = settingsData ? JSON.parse(settingsData) : DEFAULT_SETTINGS;
        const selectedGroupId = selectedGroupData || 'all';

        set({
          configs,
          groups,
          settings,
          selectedGroupId,
          isLoading: false,
        });
      } catch (error) {
        console.error('Failed to load data:', error);
        set({ isLoading: false });
      }
    },

    // 获取过滤后的配置
    getFilteredConfigs: () => {
      const { configs, selectedGroupId } = get();

      if (!selectedGroupId || selectedGroupId === 'all') {
        return configs;
      }

      return configs.filter((config) => config.groupId === selectedGroupId);
    },
  }))
);

// 监听状态变化并持久化数据
useAppStore.subscribe(
  (state) => state.configs,
  (configs) => {
    SecureStore.setItemAsync(STORAGE_KEYS.CONFIGS, JSON.stringify(configs));
  }
);

useAppStore.subscribe(
  (state) => state.groups,
  (groups) => {
    SecureStore.setItemAsync(STORAGE_KEYS.GROUPS, JSON.stringify(groups));
  }
);

useAppStore.subscribe(
  (state) => state.settings,
  (settings) => {
    SecureStore.setItemAsync(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
  }
);

useAppStore.subscribe(
  (state) => state.selectedGroupId,
  (selectedGroupId) => {
    if (selectedGroupId) {
      SecureStore.setItemAsync(STORAGE_KEYS.SELECTED_GROUP, selectedGroupId);
    }
  }
);
