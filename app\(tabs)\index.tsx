import { router } from 'expo-router';
import { Edit3, Plus } from 'lucide-react-native';
import React, { useState } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';

import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';

export default function HomeScreen() {
  const { t } = useTranslation();
  const colorScheme = useColorScheme();
  const { groups, selectedGroupId, setSelectedGroup, getFilteredConfigs } = useAppStore();
  const [showGroupEdit, setShowGroupEdit] = useState(false);

  const filteredGroups = groups.filter(group => group.id !== 'all');
  const configs = getFilteredConfigs();

  const handleAddConfig = () => {
    router.push('/add-config');
  };

  const handleSettings = () => {
    router.push('/(tabs)/settings');
  };

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroup(groupId);
  };

  const handleEditGroups = () => {
    setShowGroupEdit(true);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* 顶部导航栏 - 删除底部横线 */}
      <View style={styles.header}>
        <Text className="text-2xl font-bold">{t('home.title')}</Text>
        <View style={styles.headerButtons}>
          <Button
            variant="ghost"
            size="icon"
            onPress={handleAddConfig}
          >
            <Plus strokeWidth={2.8} size={25} className="text-primary-foreground" />
          </Button>
        </View>
      </View>

      {/* 分组按钮组 - 删除底部横线 */}
      <View style={styles.groupSection}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.groupScrollView}
          contentContainerStyle={styles.groupContainer}
        >
          {/* 全部分组按钮 */}
          <Button
            variant={selectedGroupId === 'all' ? 'default' : 'secondary'}
            size="sm"
            onPress={() => handleGroupSelect('all')}
            style={styles.groupButton}
          >
            <Text>{t('common.all')}</Text>
          </Button>

          {/* 其他分组按钮 */}
          {filteredGroups.map((group) => (
            <Button
              key={group.id}
              variant={selectedGroupId === group.id ? 'default' : 'secondary'}
              size="sm"
              onPress={() => handleGroupSelect(group.id)}
              style={styles.groupButton}
            >
              <Text>{group.name}</Text>
            </Button>
          ))}

          {/* 编辑按钮 */}
          <Button
            variant="secondary"
            size="sm"
            onPress={handleEditGroups}
            style={styles.editButton}
          >
            <Edit3 size={16} className="text-foreground" />
          </Button>
        </ScrollView>
      </View>

      {/* 配置列表区域 */}
      <ScrollView style={styles.configList}>
        {configs.length === 0 ? (
          <View style={styles.emptyState}>
            <Text className="text-lg text-muted-foreground text-center">
              {t('home.noConfigurations')}
            </Text>
            <Text className="text-sm text-muted-foreground text-center mt-2">
              {t('home.addFirstConfig')}
            </Text>
            <Button
              variant="default"
              onPress={handleAddConfig}
              style={styles.addFirstButton}
            >
              <Text>{t('home.addConfiguration')}</Text>
            </Button>
          </View>
        ) : (
          <View style={styles.configGrid}>
            {configs.map((config) => (
              <View key={config.id} style={styles.configCard}>
                <Text className="text-lg font-semibold">{config.name}</Text>
                <Text className="text-sm text-muted-foreground">{config.type.toUpperCase()}</Text>
                <Text className="text-sm text-muted-foreground">{config.url}</Text>
                {/* 这里将来会显示监控状态 */}
                <View style={styles.statusPlaceholder}>
                  <Text className="text-xs text-muted-foreground">监控状态 - 待实现</Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    // 删除底部横线
    // borderBottomWidth: 1,
    // borderBottomColor: '#e5e5e5',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  settingsButton: {
    marginRight: 8,
  },
  groupSection: {
    paddingVertical: 12,
    // 删除底部横线
    // borderBottomWidth: 1,
    // borderBottomColor: '#e5e5e5',
  },
  groupScrollView: {
    paddingHorizontal: 16,
  },
  groupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  groupButton: {
    marginRight: 8,
  },
  editButton: {
    marginLeft: 8,
  },
  configList: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  addFirstButton: {
    marginTop: 16,
  },
  configGrid: {
    gap: 12,
  },
  configCard: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    marginBottom: 12,
  },
  statusPlaceholder: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
  },
});
