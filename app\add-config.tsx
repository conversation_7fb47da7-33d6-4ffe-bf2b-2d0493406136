import { router } from 'expo-router';
import React, { useState } from 'react';
import { Modal, SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Text } from '@/components/ui/text';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { ConfigFormData, ConfigType } from '@/lib/types';
import { SUIConfigForm } from '@/components/s-ui';
import { ThreeXUIConfigForm } from '@/components/3x-ui';
import { XUIConfigForm } from '@/components/x-ui';

export default function AddConfigScreen() {
  const { t } = useTranslation();
  const { addConfig } = useAppStore();
  const [selectedType, setSelectedType] = useState<ConfigType | null>(null);
  const [showConfigForm, setShowConfigForm] = useState(false);

  const configTypes: { type: ConfigType; title: string; description: string }[] = [
    {
      type: 's-ui',
      title: t('configTypes.s-ui'),
      description: 'Sing-box UI 管理面板',
    },
    {
      type: 'x-ui',
      title: t('configTypes.x-ui'),
      description: 'X-UI 管理面板',
    },
    {
      type: '3x-ui',
      title: t('configTypes.3x-ui'),
      description: '3X-UI 管理面板',
    },
  ];

  const handleTypeSelect = (type: ConfigType) => {
    setSelectedType(type);
    setShowConfigForm(true);
  };

  const handleCloseForm = () => {
    setShowConfigForm(false);
    setSelectedType(null);
  };

  const handleFormSubmit = async (formData: ConfigFormData) => {
    if (!selectedType) return;

    try {
      // 使用store的addConfig方法
      await addConfig(formData, selectedType);

      // 关闭模态框并返回主页
      setShowConfigForm(false);
      setSelectedType(null);
      router.back();
    } catch (error) {
      console.error('Failed to add config:', error);
      // 这里可以添加错误提示
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.header}>
          <Text className="text-lg text-muted-foreground text-center">
            {t('addConfig.selectType')}
          </Text>
        </View>

        <View style={styles.typeList}>
          {configTypes.map((config) => (
            <Card key={config.type} style={styles.typeCard}>
              <View style={styles.cardContent}>
                <Text className="text-xl font-semibold">{config.title}</Text>
                <Text className="text-sm text-muted-foreground mt-2">
                  {config.description}
                </Text>
                <Button
                  variant="default"
                  onPress={() => handleTypeSelect(config.type)}
                  style={styles.selectButton}
                >
                  <Text>{t('common.add')}</Text>
                </Button>
              </View>
            </Card>
          ))}
        </View>
      </ScrollView>

      {/* 配置表单模态框 */}
      <Modal
        visible={showConfigForm}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleCloseForm}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Button
              variant="ghost"
              onPress={handleCloseForm}
            >
              <Text>{t('common.cancel')}</Text>
            </Button>
            <Text className="text-lg font-semibold">
              {selectedType && t('addConfig.title')} - {selectedType?.toUpperCase()}
            </Text>
            <View style={{ width: 60 }} />
          </View>

          <View style={styles.modalContent}>
            {selectedType === 's-ui' && (
              <SUIConfigForm
                onSubmit={handleFormSubmit}
                onCancel={handleCloseForm}
              />
            )}
            {selectedType === 'x-ui' && (
              <XUIConfigForm
                onSubmit={handleFormSubmit}
                onCancel={handleCloseForm}
              />
            )}
            {selectedType === '3x-ui' && (
              <ThreeXUIConfigForm
                onSubmit={handleFormSubmit}
                onCancel={handleCloseForm}
              />
            )}
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  typeList: {
    gap: 16,
  },
  typeCard: {
    padding: 0,
  },
  cardContent: {
    padding: 20,
  },
  selectButton: {
    marginTop: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  modalContent: {
    flex: 1,
  },
  formPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  submitButton: {
    marginTop: 24,
  },
});
