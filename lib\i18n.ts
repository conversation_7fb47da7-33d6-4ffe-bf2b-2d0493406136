import * as Localization from 'expo-localization';
import { Language } from './types';

// 翻译文本定义
export const translations = {
  en: {
    // 通用
    common: {
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      cancel: 'Cancel',
      confirm: 'Confirm',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      name: 'Name',
      url: 'URL',
      username: '<PERSON>rna<PERSON>',
      password: 'Password',
      protocol: 'Protocol',
      certificate: 'Certificate',
      group: 'Group',
      all: 'All',
      default: 'Default',
    },
    
    // 导航
    navigation: {
      home: 'Home',
      settings: 'Settings',
      addConfig: 'Add Configuration',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'UI Manager',
      addConfiguration: 'Add Configuration',
      editGroups: 'Edit Groups',
      noConfigurations: 'No configurations yet',
      addFirstConfig: 'Add your first configuration',
    },
    
    // 添加配置
    addConfig: {
      title: 'Add Configuration',
      selectType: 'Select Configuration Type',
      configName: 'Configuration Name',
      apiKey: 'API Key',
      httpWarning: 'This means API and other sensitive information will be transmitted in plain text. Please ensure you are in a secure network environment.',
      certTooltip: 'Please fill in if using self-signed certificate',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: 'Select Group',
      submit: 'Add Configuration',
    },
    
    // 分组管理
    groups: {
      title: 'Manage Groups',
      addGroup: 'Add Group',
      rename: 'Rename',
      moveUp: 'Move Up',
      moveDown: 'Move Down',
      deleteGroup: 'Delete Group',
      deleteConfirm: 'Are you sure you want to delete this group? All configurations in this group will be moved to the default group.',
    },
    
    // 设置
    settings: {
      title: 'Settings',
      theme: 'Theme',
      language: 'Language',
      proPlan: 'Pro Plan',
      subscribeToPro: 'Subscribe to Pro',
      messengerGroup: 'Messenger Group',
      themes: {
        light: 'Light',
        dark: 'Dark',
        system: 'Follow System',
      },
      languages: {
        en: 'English',
        'zh-CN': 'Simplified Chinese',
        'zh-TW': 'Traditional Chinese',
        fa: 'Persian',
      },
    },
  },
  
  'zh-CN': {
    // 通用
    common: {
      add: '添加',
      edit: '编辑',
      delete: '删除',
      save: '保存',
      cancel: '取消',
      confirm: '确认',
      loading: '加载中...',
      error: '错误',
      success: '成功',
      name: '名称',
      url: '地址',
      username: '用户名',
      password: '密码',
      protocol: '协议',
      certificate: '证书',
      group: '分组',
      all: '全部',
      default: '默认',
    },
    
    // 导航
    navigation: {
      home: '主页',
      settings: '设置',
      addConfig: '添加配置',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'UI 管理器',
      addConfiguration: '添加配置',
      editGroups: '编辑分组',
      noConfigurations: '暂无配置',
      addFirstConfig: '添加您的第一个配置',
    },
    
    // 添加配置
    addConfig: {
      title: '添加配置',
      selectType: '选择配置类型',
      configName: '配置名称',
      apiKey: 'API 密钥',
      httpWarning: '这意味着 API 等敏感信息以明文传输，请确保在安全的网络环境中使用。',
      certTooltip: '如果为自签证书请填写',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: '选择分组',
      submit: '添加配置',
    },
    
    // 分组管理
    groups: {
      title: '管理分组',
      addGroup: '添加分组',
      rename: '重命名',
      moveUp: '上移',
      moveDown: '下移',
      deleteGroup: '删除分组',
      deleteConfirm: '确定要删除此分组吗？该分组下的所有配置将移动到默认分组。',
    },
    
    // 设置
    settings: {
      title: '设置',
      theme: '主题',
      language: '语言',
      proPlan: 'Pro 计划',
      subscribeToPro: '订阅 Pro',
      messengerGroup: 'Messenger 群组',
      themes: {
        light: '浅色',
        dark: '深色',
        system: '跟随系统',
      },
      languages: {
        en: 'English',
        'zh-CN': '简体中文',
        'zh-TW': '繁体中文',
        fa: 'فارسی',
      },
    },
  },
  
  'zh-TW': {
    // 通用
    common: {
      add: '新增',
      edit: '編輯',
      delete: '刪除',
      save: '儲存',
      cancel: '取消',
      confirm: '確認',
      loading: '載入中...',
      error: '錯誤',
      success: '成功',
      name: '名稱',
      url: '網址',
      username: '使用者名稱',
      password: '密碼',
      protocol: '協定',
      certificate: '憑證',
      group: '群組',
      all: '全部',
      default: '預設',
    },
    
    // 导航
    navigation: {
      home: '主頁',
      settings: '設定',
      addConfig: '新增設定',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'UI 管理器',
      addConfiguration: '新增設定',
      editGroups: '編輯群組',
      noConfigurations: '暫無設定',
      addFirstConfig: '新增您的第一個設定',
    },
    
    // 添加配置
    addConfig: {
      title: '新增設定',
      selectType: '選擇設定類型',
      configName: '設定名稱',
      apiKey: 'API 金鑰',
      httpWarning: '這意味著 API 等敏感資訊以明文傳輸，請確保在安全的網路環境中使用。',
      certTooltip: '如果為自簽憑證請填寫',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: '選擇群組',
      submit: '新增設定',
    },
    
    // 分组管理
    groups: {
      title: '管理群組',
      addGroup: '新增群組',
      rename: '重新命名',
      moveUp: '上移',
      moveDown: '下移',
      deleteGroup: '刪除群組',
      deleteConfirm: '確定要刪除此群組嗎？該群組下的所有設定將移動到預設群組。',
    },
    
    // 设置
    settings: {
      title: '設定',
      theme: '主題',
      language: '語言',
      proPlan: 'Pro 方案',
      subscribeToPro: '訂閱 Pro',
      messengerGroup: 'Messenger 群組',
      themes: {
        light: '淺色',
        dark: '深色',
        system: '跟隨系統',
      },
      languages: {
        en: 'English',
        'zh-CN': '简体中文',
        'zh-TW': '繁體中文',
        fa: 'فارسی',
      },
    },
  },
  
  fa: {
    // 通用
    common: {
      add: 'افزودن',
      edit: 'ویرایش',
      delete: 'حذف',
      save: 'ذخیره',
      cancel: 'لغو',
      confirm: 'تأیید',
      loading: 'در حال بارگذاری...',
      error: 'خطا',
      success: 'موفقیت',
      name: 'نام',
      url: 'آدرس',
      username: 'نام کاربری',
      password: 'رمز عبور',
      protocol: 'پروتکل',
      certificate: 'گواهی',
      group: 'گروه',
      all: 'همه',
      default: 'پیش‌فرض',
    },
    
    // 导航
    navigation: {
      home: 'خانه',
      settings: 'تنظیمات',
      addConfig: 'افزودن پیکربندی',
    },
    
    // 配置类型
    configTypes: {
      's-ui': 'S-UI',
      'x-ui': 'X-UI',
      '3x-ui': '3X-UI',
    },
    
    // 主屏幕
    home: {
      title: 'مدیر UI',
      addConfiguration: 'افزودن پیکربندی',
      editGroups: 'ویرایش گروه‌ها',
      noConfigurations: 'هنوز پیکربندی وجود ندارد',
      addFirstConfig: 'اولین پیکربندی خود را اضافه کنید',
    },
    
    // 添加配置
    addConfig: {
      title: 'افزودن پیکربندی',
      selectType: 'انتخاب نوع پیکربندی',
      configName: 'نام پیکربندی',
      apiKey: 'کلید API',
      httpWarning: 'این بدان معناست که API و سایر اطلاعات حساس به صورت متن ساده ارسال می‌شوند. لطفاً اطمینان حاصل کنید که در محیط شبکه امنی هستید.',
      certTooltip: 'اگر از گواهی خودامضا استفاده می‌کنید، لطفاً پر کنید',
      certPlaceholder: '-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----',
      selectGroup: 'انتخاب گروه',
      submit: 'افزودن پیکربندی',
    },
    
    // 分组管理
    groups: {
      title: 'مدیریت گروه‌ها',
      addGroup: 'افزودن گروه',
      rename: 'تغییر نام',
      moveUp: 'انتقال به بالا',
      moveDown: 'انتقال به پایین',
      deleteGroup: 'حذف گروه',
      deleteConfirm: 'آیا مطمئن هستید که می‌خواهید این گروه را حذف کنید؟ تمام پیکربندی‌های این گروه به گروه پیش‌فرض منتقل خواهند شد.',
    },
    
    // 设置
    settings: {
      title: 'تنظیمات',
      theme: 'تم',
      language: 'زبان',
      proPlan: 'طرح Pro',
      subscribeToPro: 'اشتراک Pro',
      messengerGroup: 'گروه Messenger',
      themes: {
        light: 'روشن',
        dark: 'تیره',
        system: 'پیروی از سیستم',
      },
      languages: {
        en: 'English',
        'zh-CN': '简体中文',
        'zh-TW': '繁體中文',
        fa: 'فارسی',
      },
    },
  },
};

// 获取当前语言
export const getCurrentLanguage = (): Language => {
  const locale = Localization.getLocales()[0];
  const languageTag = locale.languageTag;
  
  if (languageTag.startsWith('zh')) {
    return locale.regionCode === 'TW' || locale.regionCode === 'HK' ? 'zh-TW' : 'zh-CN';
  }
  
  if (languageTag.startsWith('fa')) {
    return 'fa';
  }
  
  return 'en';
};

// 翻译函数
export const t = (key: string, language: Language = 'en'): string => {
  const keys = key.split('.');
  let value: any = translations[language];
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // 如果找不到翻译，回退到英文
      value = translations.en;
      for (const fallbackKey of keys) {
        if (value && typeof value === 'object' && fallbackKey in value) {
          value = value[fallbackKey];
        } else {
          return key; // 如果英文也没有，返回原始 key
        }
      }
      break;
    }
  }
  
  return typeof value === 'string' ? value : key;
};
